@echo off
chcp 65001 >nul
title 创建桌面快捷方式

echo.
echo ========================================
echo    🎪 创建Hajimi King桌面快捷方式
echo ========================================
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\Desktop"

echo 当前目录: %current_dir%
echo 桌面目录: %desktop%
echo.

:: 创建启动快捷方式
echo 🚀 创建启动快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\Hajimi King - 启动.lnk'); $Shortcut.TargetPath = '%current_dir%启动.bat'; $Shortcut.WorkingDirectory = '%current_dir%'; $Shortcut.IconLocation = 'shell32.dll,25'; $Shortcut.Description = 'Hajimi King 快速启动'; $Shortcut.Save()"

if exist "%desktop%\Hajimi King - 启动.lnk" (
    echo ✅ 启动快捷方式创建成功
) else (
    echo ❌ 启动快捷方式创建失败
)

:: 创建管理工具快捷方式
echo 🛠️  创建管理工具快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\Hajimi King - 管理.lnk'); $Shortcut.TargetPath = '%current_dir%快速管理.bat'; $Shortcut.WorkingDirectory = '%current_dir%'; $Shortcut.IconLocation = 'shell32.dll,16'; $Shortcut.Description = 'Hajimi King 管理工具'; $Shortcut.Save()"

if exist "%desktop%\Hajimi King - 管理.lnk" (
    echo ✅ 管理工具快捷方式创建成功
) else (
    echo ❌ 管理工具快捷方式创建失败
)

:: 创建数据目录快捷方式
echo 📁 创建数据目录快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%desktop%\Hajimi King - 数据.lnk'); $Shortcut.TargetPath = '%current_dir%data'; $Shortcut.WorkingDirectory = '%current_dir%data'; $Shortcut.IconLocation = 'shell32.dll,4'; $Shortcut.Description = 'Hajimi King 数据目录'; $Shortcut.Save()"

if exist "%desktop%\Hajimi King - 数据.lnk" (
    echo ✅ 数据目录快捷方式创建成功
) else (
    echo ❌ 数据目录快捷方式创建失败
)

echo.
echo ========================================
echo    ✅ 快捷方式创建完成!
echo ========================================
echo.
echo 已在桌面创建以下快捷方式:
echo 🚀 Hajimi King - 启动.lnk
echo 🛠️  Hajimi King - 管理.lnk  
echo 📁 Hajimi King - 数据.lnk
echo.
echo 现在您可以直接从桌面启动和管理Hajimi King了!
echo.
pause
