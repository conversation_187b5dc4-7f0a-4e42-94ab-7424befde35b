@echo off
chcp 65001 >nul
title <PERSON><PERSON><PERSON> King - 一键启动

echo.
echo ========================================
echo    🎪 Hajimi King 一键启动 🏆
echo ========================================
echo    (绕过PowerShell执行策略限制)
echo ========================================
echo.

:: 检查Python环境
echo [1/4] 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.11+
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

:: 检查依赖
echo.
echo [2/4] 📦 检查依赖包...
python -c "import google.generativeai, dotenv, requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  依赖包缺失，正在安装...
    pip install google-generativeai python-dotenv requests
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 依赖包检查完成

:: 检查配置文件
echo.
echo [3/4] ⚙️  检查配置文件...
if not exist ".env" (
    echo ⚠️  .env文件不存在，正在创建...
    copy env.example .env >nul
    echo ✅ 已创建.env文件
    echo.
    echo ⚠️  请先配置GitHub Token:
    echo 1. 访问: https://github.com/settings/tokens
    echo 2. 创建新token，权限选择 public_repo
    echo 3. 复制token到.env文件的GITHUB_TOKENS=后面
    echo.
    echo 是否现在打开.env文件进行配置? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        notepad .env
        echo.
        echo 配置完成后按任意键继续...
        pause >nul
    )
)

if not exist "data\queries.txt" (
    echo ⚠️  queries.txt文件不存在，正在创建默认配置...
    if not exist "data" mkdir data
    echo # GitHub搜索查询配置文件 > data\queries.txt
    echo # 每行一个查询语句，支持GitHub搜索语法 >> data\queries.txt
    echo # 以#开头的行为注释，空行会被忽略 >> data\queries.txt
    echo. >> data\queries.txt
    echo AIzaSy in:file >> data\queries.txt
    echo ✅ 已创建默认查询配置
)

:: 检查GitHub Token
findstr /C:"GITHUB_TOKENS=" .env | findstr /V /C:"GITHUB_TOKENS=$" | findstr /V /C:"GITHUB_TOKENS= " >nul
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  警告: 未检测到有效的GitHub Token配置
    echo 请在.env文件中配置GITHUB_TOKENS
    echo.
    echo 是否现在配置? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        notepad .env
        echo 配置完成后按任意键继续...
        pause >nul
    ) else (
        echo.
        echo 是否继续启动? (y/n)
        set /p choice2=
        if /i not "%choice2%"=="y" (
            echo 已取消启动
            pause
            exit /b 0
        )
    )
)

echo ✅ 配置检查完成

:: 启动程序
echo.
echo [4/4] 🚀 启动Hajimi King...
echo.
echo ========================================
echo    程序启动中，请稍候...
echo    按 Ctrl+C 可停止程序
echo ========================================
echo.

:: 设置Python路径并启动
set PYTHONPATH=.
python app/hajimi_king.py

echo.
echo ========================================
echo    程序已停止
echo ========================================
pause
