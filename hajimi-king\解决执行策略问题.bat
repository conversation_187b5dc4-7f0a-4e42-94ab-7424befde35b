@echo off
chcp 65001 >nul
title 解决PowerShell执行策略问题

echo.
echo ========================================
echo    🔧 解决PowerShell执行策略问题
echo ========================================
echo.

echo 检测到PowerShell执行策略限制，正在为您提供解决方案...
echo.

echo 方案1: 临时允许执行 (推荐)
echo ----------------------------------------
echo 运行以下命令 (管理员权限):
echo powershell -ExecutionPolicy Bypass -File "启动.ps1"
echo.

echo 方案2: 永久修改执行策略 (需要管理员权限)
echo ----------------------------------------
echo Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
echo.

echo 方案3: 使用批处理文件 (无需修改策略)
echo ----------------------------------------
echo 直接使用 启动.bat 文件，功能完全相同
echo.

echo 正在尝试方案1...
echo.

powershell -ExecutionPolicy Bypass -File "启动.ps1"

if %errorlevel% neq 0 (
    echo.
    echo ❌ 方案1失败，请尝试以下解决方案:
    echo.
    echo 1. 右键点击PowerShell，选择"以管理员身份运行"
    echo 2. 运行: Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
    echo 3. 或者直接使用 启动.bat 文件
    echo.
    pause
) else (
    echo.
    echo ✅ 问题已解决!
)
