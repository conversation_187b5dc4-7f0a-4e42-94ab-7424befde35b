@echo off
chcp 65001 >nul

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ========================================
    echo    🔧 需要管理员权限
    echo ========================================
    echo.
    echo 此脚本需要管理员权限来修改PowerShell执行策略
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

title 修复PowerShell执行策略 - 管理员模式

echo.
echo ========================================
echo    🔧 修复PowerShell执行策略
echo ========================================
echo.

echo 当前执行策略:
powershell -Command "Get-ExecutionPolicy -List"
echo.

echo 正在修改执行策略为 RemoteSigned...
powershell -Command "Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"

if %errorlevel% equ 0 (
    echo ✅ 执行策略修改成功!
    echo.
    echo 修改后的执行策略:
    powershell -Command "Get-ExecutionPolicy -List"
    echo.
    echo 现在您可以正常运行PowerShell脚本了!
) else (
    echo ❌ 执行策略修改失败
    echo.
    echo 请尝试手动修改:
    echo 1. 以管理员身份打开PowerShell
    echo 2. 运行: Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
)

echo.
echo 或者您可以直接使用批处理文件:
echo - 一键启动(无策略限制).bat
echo - 启动.bat
echo.
pause
