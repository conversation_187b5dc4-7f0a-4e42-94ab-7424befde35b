@echo off
chcp 65001 >nul
title Haji<PERSON> King - 直接启动

echo.
echo ========================================
echo    🎪 Hajimi King 直接启动 🏆
echo ========================================
echo.

echo [1/3] 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装，请先安装Python
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo.
echo [2/3] 📦 检查依赖包...
python -c "import google.generativeai, dotenv, requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  正在安装依赖包...
    pip install google-generativeai python-dotenv requests
)
echo ✅ 依赖包正常

echo.
echo [3/3] 🚀 启动程序...
echo.
echo ========================================
echo    程序运行中...
echo    按 Ctrl+C 停止程序
echo ========================================
echo.

set PYTHONPATH=.
python app/hajimi_king.py

echo.
echo 程序已停止
pause
