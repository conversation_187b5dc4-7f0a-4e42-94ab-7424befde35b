# Hajimi King 本地部署说明

## 🎉 部署成功！

Hajimi King 项目已经成功在本地部署并运行。

## 📋 部署步骤总结

### 1. 环境检查 ✅
- **Python版本**: 3.11.5 ✅
- **pip版本**: 25.1.1 ✅
- **uv包管理器**: 0.6.14 ✅

### 2. 项目克隆 ✅
```bash
git clone https://github.com/GakkiNoOne/hajimi-king.git
cd hajimi-king
```

### 3. 依赖安装 ✅
```bash
pip install google-generativeai python-dotenv requests
```

### 4. 配置文件设置 ✅
- ✅ `.env` 文件已配置
- ✅ `queries.txt` 文件已创建
- ✅ `data` 目录已创建

### 5. 程序运行 ✅
```bash
$env:PYTHONPATH="."; python app/hajimi_king.py
```

## 📊 当前运行状态

### 系统信息
- 🔑 GitHub tokens: 1个已配置
- 🔍 搜索查询: 3个已加载
- 📅 日期过滤: 90天
- 💾 扫描模式: 全量扫描

### 文件结构
```
hajimi-king/
├── data/
│   ├── keys/
│   │   ├── keys_valid_20250801.txt     # 有效密钥文件
│   │   ├── key_429_20250801.txt        # 速率限制密钥文件
│   │   └── keys_send_20250801.txt      # 发送密钥文件
│   ├── logs/
│   │   ├── keys_valid_detail_20250801.log    # 详细日志
│   │   ├── key_429_detail_20250801.log       # 速率限制日志
│   │   └── keys_send_detail_20250801.log     # 发送日志
│   ├── checkpoint.json                 # 检查点文件
│   ├── queries.txt                     # 搜索查询配置
│   └── scanned_shas.txt               # 已扫描文件记录
```

## 🔧 使用说明

### 启动程序
```bash
cd hajimi-king
$env:PYTHONPATH="."; python app/hajimi_king.py
```

### 查看日志
```bash
# 查看有效密钥详细日志
Get-Content data/logs/keys_valid_detail_20250801.log -Tail 20

# 查看找到的有效密钥
Get-Content data/keys/keys_valid_20250801.txt
```

### 停止程序
按 `Ctrl + C` 停止程序

## ⚠️ 注意事项

### GitHub API 速率限制
- 程序运行时遇到了GitHub API速率限制，这是正常现象
- GitHub API对未认证请求有严格的速率限制
- 建议配置多个GitHub Token以提高搜索效率

### 配置GitHub Token
1. 访问 [GitHub Settings > Tokens](https://github.com/settings/tokens)
2. 创建具有 `public_repo` 权限的访问令牌
3. 在 `.env` 文件中配置多个token（用逗号分隔）：
   ```
   GITHUB_TOKENS=ghp_token1,ghp_token2,ghp_token3
   ```

### 代理配置（推荐）
由于GitHub和Gemini API的访问限制，强烈建议配置代理：
```
PROXY=http://localhost:1080
```

## 🎯 搜索查询优化

当前查询配置在 `data/queries.txt`：
```
AIzaSy in:file
```

可以添加更多搜索表达式来提高搜索效率：
```
AIzaSy in:file
AIzaSy in:file filename:.env
AIzaSy in:file filename:config
```

## 📈 监控和管理

### 实时监控
```bash
# 监控日志文件
Get-Content data/logs/keys_valid_detail_20250801.log -Wait

# 检查找到的密钥数量
(Get-Content data/keys/keys_valid_20250801.txt | Measure-Object -Line).Lines
```

### 数据清理
```bash
# 清理旧的扫描记录（重新开始扫描）
Remove-Item data/scanned_shas.txt
Remove-Item data/checkpoint.json
```

## 🚀 下一步

1. **配置更多GitHub Token** - 提高搜索速度和避免速率限制
2. **设置代理** - 提高访问稳定性
3. **优化搜索查询** - 添加更多有效的搜索表达式
4. **监控结果** - 定期检查找到的密钥文件

## 🎊 部署完成

Hajimi King 已经成功部署并开始运行！程序会持续搜索GitHub上的API密钥，所有结果都会保存在 `data` 目录中。

---
*部署时间: 2025-08-01*
*部署状态: ✅ 成功*
